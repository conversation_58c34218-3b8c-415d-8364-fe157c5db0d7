import { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import heroImage from "@/assets/hero-warehouse.jpg";
import { Link } from "react-router-dom";

interface HeroProps {
  title: string;
  subtitle: string;
}

export default function Hero({ title, subtitle }: HeroProps) {
  useEffect(() => {
    const root = document.documentElement;
    const onMove = (e: MouseEvent) => {
      if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) return;
      const x = (e.clientX / window.innerWidth).toFixed(2);
      const y = (e.clientY / window.innerHeight).toFixed(2);
      root.style.setProperty('--pointer-x', x);
      root.style.setProperty('--pointer-y', y);
    };
    window.addEventListener('mousemove', onMove);
    return () => window.removeEventListener('mousemove', onMove);
  }, []);

  return (
    <section className="relative overflow-hidden">
      <div className="absolute inset-0 -z-10 bg-surface-gradient opacity-60" aria-hidden />
      <div className="container grid gap-8 py-16 md:grid-cols-2 md:py-20 lg:py-28">
        <div className="flex flex-col justify-center gap-6">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl">
            {title}
          </h1>
          <p className="text-lg text-muted-foreground max-w-prose">
            {subtitle}
          </p>
          <div className="flex flex-wrap gap-3">
            <Link to="/contact"><Button variant="hero" size="lg">Request a Quote</Button></Link>
            <Link to="/products"><Button variant="outlinePrimary" size="lg">Explore Products</Button></Link>
          </div>
        </div>
        <div className="relative">
          <img
            src={heroImage}
            alt="Modern chemical distribution warehouse with drums and IBC totes"
            className="w-full rounded-lg border shadow-elevated animate-fade-up"
            loading="eager"
          />
          <div className="absolute -bottom-6 left-6 right-6 h-24 bg-gradient-primary opacity-30 blur-2xl rounded-full pointer-events-none" aria-hidden />
        </div>
      </div>
    </section>
  );
}
