import { Link } from "react-router-dom";

export default function SiteFooter() {
  return (
    <footer className="border-t bg-background">
      <div className="container py-10 grid gap-8 md:grid-cols-4">
        <div className="space-y-4">
          <img
            src="/lovable-uploads/304f19d4-17e0-436d-9c88-92ef0a2be8c3.png"
            alt="Manochem logo"
            className="h-8 w-auto"
            loading="lazy"
            decoding="async"
          />
          <p className="text-sm text-muted-foreground max-w-sm">
            Safe, compliant chemical distribution with global sourcing and local reliability.
          </p>
        </div>
        <div>
          <h3 className="font-semibold mb-4">Company</h3>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li><Link to="/about" className="hover:text-primary">About</Link></li>
            <li><Link to="/quality" className="hover:text-primary">Quality & Compliance</Link></li>
            <li><Link to="/sustainability" className="hover:text-primary">Sustainability</Link></li>
          </ul>
        </div>
        <div>
          <h3 className="font-semibold mb-4">Solutions</h3>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li><Link to="/products" className="hover:text-primary">Products</Link></li>
            <li><Link to="/industries" className="hover:text-primary">Industries</Link></li>
            <li><Link to="/services" className="hover:text-primary">Services</Link></li>
          </ul>
        </div>
        <div>
          <h3 className="font-semibold mb-4">Get in touch</h3>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li><Link to="/contact" className="hover:text-primary">Contact</Link></li>
            <li><Link to="/sds" className="hover:text-primary">SDS Library</Link></li>
          </ul>
        </div>
      </div>
      <div className="border-t">
        <div className="container flex flex-col md:flex-row items-center justify-between py-6 text-xs text-muted-foreground">
          <p>© {new Date().getFullYear()} Manochem. All rights reserved.</p>
          <p>ISO 9001 | Responsible Care | REACH-ready</p>
        </div>
      </div>
    </footer>
  );
}
