import { Link, NavLink } from "react-router-dom";

const navItems = [
  { to: "/products", label: "Products" },
  { to: "/industries", label: "Industries" },
  { to: "/services", label: "Services" },
  { to: "/quality", label: "Quality" },
  { to: "/sustainability", label: "Sustainability" },
  { to: "/sds", label: "SDS" },
  { to: "/about", label: "About" },
  { to: "/contact", label: "Contact" },
];

export default function SiteHeader() {
  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background/80 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        <Link to="/" className="flex items-center gap-3" aria-label="Manochem Home">
          <img
            src="/lovable-uploads/304f19d4-17e0-436d-9c88-92ef0a2be8c3.png"
            alt="Manochem logo - orange arc with wordmark"
            className="h-8 w-auto"
            loading="lazy"
            decoding="async"
          />
        </Link>
        <nav className="hidden md:flex items-center gap-6" aria-label="Primary">
          {navItems.map((item) => (
            <NavLink
              key={item.to}
              to={item.to}
              className={({ isActive }) =>
                `text-sm transition-colors hover:text-primary ${isActive ? "text-primary" : "text-muted-foreground"}`
              }
            >
              {item.label}
            </NavLink>
          ))}
        </nav>
        <div className="hidden md:block">
          <Link to="/contact" className="inline-flex h-10 items-center rounded-md bg-primary px-4 text-sm font-medium text-primary-foreground shadow-elevated transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
            Request Quote
          </Link>
        </div>
      </div>
    </header>
  );
}
