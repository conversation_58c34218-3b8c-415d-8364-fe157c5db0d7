import { useState } from "react";
import SEO from "@/components/SEO";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { Phone, Mail } from "lucide-react";

export default function Contact() {
  const [loading, setLoading] = useState(false);

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      toast({ title: 'Message sent', description: 'We\'ll get back to you within 1 business day.' });
    }, 800);
  };

  return (
    <main className="container py-12">
      <SEO
        title="Contact | Manochem"
        description="Request a quote or speak with our team about products, supply programs, and regulatory support."
        canonical="/contact"
      />
      <h1 className="mb-6 text-4xl font-bold">Contact</h1>
      <div className="grid gap-10 md:grid-cols-2">
        <form onSubmit={onSubmit} className="space-y-4">
          <div className="grid gap-4 sm:grid-cols-2">
            <Input required name="name" placeholder="Full name" />
            <Input required type="email" name="email" placeholder="Work email" />
          </div>
          <Input name="company" placeholder="Company" />
          <Input name="phone" placeholder="Phone" />
          <Textarea required name="message" placeholder="How can we help?" rows={6} />
          <Button type="submit" variant="hero" disabled={loading}>{loading ? 'Sending…' : 'Send message'}</Button>
        </form>
        <aside className="space-y-6">
          <p className="text-muted-foreground">Prefer speaking to someone? Our specialists can help specify materials, packaging, and delivery cadence.</p>
          <div className="space-y-3 text-sm">
            <p className="flex items-center gap-2"><Phone className="text-primary" /> +****************</p>
            <p className="flex items-center gap-2"><Mail className="text-primary" /> <EMAIL></p>
          </div>
        </aside>
      </div>
    </main>
  );
}
