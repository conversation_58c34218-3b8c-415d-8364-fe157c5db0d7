import Hero from "@/components/Hero";
import SEO from "@/components/SEO";
import { Card, CardContent } from "@/components/ui/card";
import industriesImg from "@/assets/industries-montage.jpg";
import sustainImg from "@/assets/sustainability-green-plant.jpg";
import { ShieldCheck, Truck, Globe2 } from "lucide-react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";

const Index = () => {
  return (
    <main>
      <SEO
        title="Manochem | Chemical Distribution & Supply"
        description="Trusted chemical distributor for pharma, food, and industrial markets. Safe, compliant logistics with SDS/COA and full traceability."
        canonical="/"
        jsonLd={{
          '@context': 'https://schema.org',
          '@type': 'Organization',
          name: 'Manochem',
          url: '/',
          logo: '/lovable-uploads/304f19d4-17e0-436d-9c88-92ef0a2be8c3.png',
          sameAs: [],
        }}
      />

      <Hero
        title="Safe, compliant chemical supply—delivered on time"
        subtitle="From audited global producers to your line with full batch traceability, regulatory labeling, and returnable packaging options."
      />

      <section className="container grid gap-6 py-12 md:grid-cols-3">
        {[{
          icon: Truck, title: 'Reliable logistics', desc: 'ADR/HAZMAT-compliant network with last‑mile visibility.'
        },{
          icon: ShieldCheck, title: 'Quality you can prove', desc: 'ISO 9001 QMS, COA & SDS linked to every batch.'
        },{
          icon: Globe2, title: 'Global sourcing', desc: 'Audited supply base with multi‑sourcing for resilience.'
        }].map(({icon: Icon, title, desc}) => (
          <Card key={title} className="p-6">
            <div className="flex items-start gap-3">
              <Icon className="text-primary" />
              <div>
                <h2 className="text-lg font-semibold">{title}</h2>
                <p className="mt-1 text-sm text-muted-foreground">{desc}</p>
              </div>
            </div>
          </Card>
        ))}
      </section>

      <section className="container grid gap-8 py-12 md:grid-cols-2">
        <Card className="overflow-hidden">
          <img src={industriesImg} alt="Industries montage" className="h-64 w-full object-cover" loading="lazy" />
          <CardContent className="p-6">
            <h2 className="mb-2 text-2xl font-semibold">Industries we serve</h2>
            <p className="text-muted-foreground">Pharma & biotech, food & beverage, personal care, coatings, automotive, and more.</p>
            <div className="mt-4"><Link to="/industries"><Button variant="outlinePrimary">Learn more</Button></Link></div>
          </CardContent>
        </Card>
        <Card className="overflow-hidden">
          <img src={sustainImg} alt="Sustainability visual" className="h-64 w-full object-cover" loading="lazy" />
          <CardContent className="p-6">
            <h2 className="mb-2 text-2xl font-semibold">Sustainability in practice</h2>
            <p className="text-muted-foreground">Lower‑carbon logistics and returnable packaging to reduce waste and risk.</p>
            <div className="mt-4"><Link to="/sustainability"><Button variant="outlinePrimary">Our approach</Button></Link></div>
          </CardContent>
        </Card>
      </section>

      <section className="container py-16">
        <Card className="bg-accent text-accent-foreground">
          <CardContent className="flex flex-col items-start gap-4 p-8 md:flex-row md:items-center md:justify-between">
            <div>
              <h2 className="text-2xl font-semibold">Need an SDS or a quote?</h2>
              <p className="text-sm opacity-90">Tell us your material, packaging, and cadence—we'll tailor a program.</p>
            </div>
            <div className="flex gap-3">
              <Link to="/sds"><Button variant="outline">Find SDS</Button></Link>
              <Link to="/contact"><Button variant="hero">Request quote</Button></Link>
            </div>
          </CardContent>
        </Card>
      </section>
    </main>
  );
};

export default Index;
