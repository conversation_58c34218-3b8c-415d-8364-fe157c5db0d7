import SEO from "@/components/SEO";
import industriesImg from "@/assets/industries-montage.jpg";

export default function Industries() {
  return (
    <main className="container py-12">
      <SEO
        title="Industries We Serve | Manochem"
        description="Pharmaceutical, food & beverage, personal care, coatings, and automotive manufacturing."
        canonical="/industries"
      />
      <h1 className="mb-6 text-4xl font-bold">Industries</h1>
      <p className="mb-8 max-w-2xl text-muted-foreground">
        From regulated environments to high-throughput manufacturing, we tailor supply programs for your sector's standards and cadence.
      </p>
      <img src={industriesImg} alt="Composite of pharma lab, food processing, and automotive line" className="mb-10 w-full rounded-lg border shadow-elevated" loading="lazy" />
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {[
          'Pharmaceutical & Biotech',
          'Food & Beverage',
          'Personal Care & Cosmetics',
          'Paints, Inks & Coatings',
          'Automotive & Aerospace',
          'Electronics & Energy',
        ].map((item) => (
          <article key={item} className="rounded-lg border p-6">
            <h2 className="text-lg font-semibold">{item}</h2>
            <p className="mt-2 text-sm text-muted-foreground">Qualified materials, validated packaging, and on-time deliveries designed for your line.</p>
          </article>
        ))}
      </div>
    </main>
  );
}
