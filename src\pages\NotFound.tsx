import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import SEO from "@/components/SEO";
import { Button } from "@/components/ui/button";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <main className="min-h-screen grid place-items-center">
      <SEO title="404 | Manochem" description="Page not found" canonical={location.pathname} />
      <div className="text-center">
        <h1 className="text-6xl font-bold">404</h1>
        <p className="mt-2 text-muted-foreground">Oops! Page not found</p>
        <a href="/" className="inline-block mt-6"><Button variant="outlinePrimary">Return to Home</Button></a>
      </div>
    </main>
  );
};

export default NotFound;
